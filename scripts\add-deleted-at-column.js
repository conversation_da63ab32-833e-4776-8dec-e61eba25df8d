// Migration script to add deleted_at column to blog_posts table if it doesn't exist
require('dotenv').config({ path: '.env.local' });
const { neon } = require('@neondatabase/serverless');

const sql = neon(process.env.DATABASE_URL);

async function addDeletedAtColumn() {
  try {
    console.log('🔍 Checking if deleted_at column exists...');

    // Check if the column already exists
    const columnCheck = await sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'blog_posts' 
      AND column_name = 'deleted_at'
    `;

    if (columnCheck.length > 0) {
      console.log('✅ deleted_at column already exists');
      return;
    }

    console.log('➕ Adding deleted_at column to blog_posts table...');

    // Add the deleted_at column
    await sql`
      ALTER TABLE blog_posts 
      ADD COLUMN deleted_at TIMESTAMP NULL
    `;

    // Add index for better performance on soft delete queries
    await sql`
      CREATE INDEX IF NOT EXISTS idx_blog_posts_deleted_at 
      ON blog_posts(deleted_at)
    `;

    console.log('✅ Successfully added deleted_at column and index');

  } catch (error) {
    console.error('❌ Error adding deleted_at column:', error);
    throw error;
  }
}

// Run the migration
addDeletedAtColumn()
  .then(() => {
    console.log('🎉 Migration completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Migration failed:', error);
    process.exit(1);
  });
